<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KG-Gen 知识图谱生成器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .result-card {
            display: none;
            margin-top: 20px;
        }
        .entity-tag {
            display: inline-block;
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.85em;
        }
        .relation-item {
            background-color: #f5f5f5;
            padding: 8px;
            margin: 4px 0;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .loading {
            display: none;
        }
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="display-4"><i class="fas fa-project-diagram text-primary"></i> KG-Gen</h1>
                    <p class="lead text-muted">智能知识图谱生成器</p>
                    <p class="text-muted">支持文本输入和文件上传（txt, csv, xlsx, json）</p>
                </div>
            </div>
        </div>

        <!-- 选项卡 -->
        <ul class="nav nav-tabs mb-4" id="inputTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-input" type="button">
                    <i class="fas fa-keyboard"></i> 文本输入
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="file-tab" data-bs-toggle="tab" data-bs-target="#file-upload" type="button">
                    <i class="fas fa-file-upload"></i> 文件上传
                </button>
            </li>
        </ul>

        <div class="tab-content" id="inputTabsContent">
            <!-- 文本输入标签页 -->
            <div class="tab-pane fade show active" id="text-input" role="tabpanel">
                <div class="card">
                    <div class="card-body">
                        <form id="textForm">
                            <div class="mb-3">
                                <label for="textContent" class="form-label">输入文本内容</label>
                                <textarea class="form-control" id="textContent" rows="8" 
                                    placeholder="请输入要分析的文本内容...&#10;&#10;示例：&#10;Linda is Josh's mother. Ben is Josh's brother. Andrew is Josh's father."></textarea>
                            </div>
                            
                            <!-- 高级选项 -->
                            <div class="accordion mb-3" id="textAdvancedOptions">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#textAdvanced">
                                            <i class="fas fa-cog"></i>&nbsp; 高级选项
                                        </button>
                                    </h2>
                                    <div id="textAdvanced" class="accordion-collapse collapse">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label for="textModel" class="form-label">AI模型</label>
                                                    <select class="form-select" id="textModel">
                                                        <option value="openai/gpt-4o-mini">GPT-4o Mini (推荐)</option>
                                                        <option value="openai/gpt-4o">GPT-4o</option>
                                                        <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="textChunkSize" class="form-label">分块大小</label>
                                                    <input type="number" class="form-control" id="textChunkSize" placeholder="留空自动处理">
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="textCluster">
                                                        <label class="form-check-label" for="textCluster">
                                                            启用聚类分析
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="textContext" class="form-label">上下文描述</label>
                                                    <input type="text" class="form-control" id="textContext" placeholder="可选，如：家庭关系">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg">
                                <span class="loading"><i class="fas fa-spinner fa-spin"></i> 生成中...</span>
                                <span class="not-loading"><i class="fas fa-magic"></i> 生成知识图谱</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 文件上传标签页 -->
            <div class="tab-pane fade" id="file-upload" role="tabpanel">
                <div class="card">
                    <div class="card-body">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>拖拽文件到这里或点击选择文件</h5>
                                <p class="text-muted">支持 .txt, .csv, .xlsx, .xls, .json 格式</p>
                                <p class="text-muted">最大文件大小: 16MB</p>
                                <input type="file" id="fileInput" name="file" accept=".txt,.csv,.xlsx,.xls,.json" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-file"></i> <span id="fileName"></span>
                                    <button type="button" class="btn-close float-end" onclick="clearFile()"></button>
                                </div>
                            </div>
                            
                            <!-- 高级选项 -->
                            <div class="accordion mt-3" id="fileAdvancedOptions">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#fileAdvanced">
                                            <i class="fas fa-cog"></i>&nbsp; 高级选项
                                        </button>
                                    </h2>
                                    <div id="fileAdvanced" class="accordion-collapse collapse">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label for="fileModel" class="form-label">AI模型</label>
                                                    <select class="form-select" id="fileModel" name="model">
                                                        <option value="openai/gpt-4o-mini">GPT-4o Mini (推荐)</option>
                                                        <option value="openai/gpt-4o">GPT-4o</option>
                                                        <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="fileChunkSize" class="form-label">分块大小</label>
                                                    <input type="number" class="form-control" id="fileChunkSize" name="chunk_size" placeholder="留空自动处理">
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="fileCluster" name="cluster">
                                                        <label class="form-check-label" for="fileCluster">
                                                            启用聚类分析
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="fileContext" class="form-label">上下文描述</label>
                                                    <input type="text" class="form-control" id="fileContext" name="context" placeholder="可选，如：技术文档">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg mt-3" disabled id="uploadBtn">
                                <span class="loading"><i class="fas fa-spinner fa-spin"></i> 处理中...</span>
                                <span class="not-loading"><i class="fas fa-magic"></i> 生成知识图谱</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="card result-card" id="resultCard">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-chart-network"></i> 知识图谱结果</h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" id="viewGraphBtn">
                        <i class="fas fa-eye"></i> 查看可视化
                    </button>
                    <button class="btn btn-outline-success btn-sm" id="downloadBtn">
                        <i class="fas fa-download"></i> 下载
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-primary" id="entitiesCount">0</h3>
                            <p class="text-muted">实体数量</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-success" id="relationsCount">0</h3>
                            <p class="text-muted">关系数量</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-warning" id="edgesCount">0</h3>
                            <p class="text-muted">关系类型</p>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-tags"></i> 提取的实体</h6>
                        <div id="entitiesList" class="mb-3"></div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-link"></i> 关系列表</h6>
                        <div id="relationsList" style="max-height: 300px; overflow-y: auto;"></div>
                    </div>
                </div>
                
                <div id="clustersInfo" style="display: none;">
                    <hr>
                    <h6><i class="fas fa-layer-group"></i> 聚类结果</h6>
                    <div id="clustersList"></div>
                </div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div class="alert alert-danger" id="errorAlert" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i> <span id="errorMessage"></span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
