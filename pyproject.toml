[project]
name = "kg-gen"
version = "0.1.6"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
]
description = "Extract a knowledge graph using LLMs from any text or messages array"
readme = "README.md"
requires-python = ">=3.10, <4.0"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
license = "MIT"
dependencies = [
    "dspy==2.6.24",
    "nltk",
    "pydantic>=2.0.0",
    "openai==1.61.0"
]

[project.optional-dependencies]
dev = [
    "pyvis==0.3.2"
]

[project.urls]
Homepage = "https://github.com/stair-lab/kg-gen"
Issues = "https://github.com/stair-lab/kg-gen/issues"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"