#!/usr/bin/env python3
"""
kg-gen 可视化演示脚本
生成知识图谱并创建交互式可视化
"""

import os
import sys
from dotenv import load_dotenv

# 设置代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kg_gen import KGGen
from tests.utils.visualize_kg import visualize

def main():
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 错误: 请在.env文件中设置OPENAI_API_KEY")
        return
    
    print("🚀 kg-gen 可视化演示")
    print("=" * 40)
    
    # 初始化KGGen
    kg = KGGen(
        model="openai/gpt-4o-mini",
        temperature=0.0,
        api_key=api_key
    )
    
    # 示例1: 哈利波特家族关系
    print("\n📝 示例1: 哈利波特家族关系")
    text1 = """
    <PERSON> has two parents - his father <PERSON> and his mother <PERSON> <PERSON>.
    <PERSON> and his wife Ginny have three children together: their oldest son <PERSON> <PERSON>,
    their other son <PERSON>bus, and their daughter <PERSON> <PERSON>. Hermione <PERSON>r is <PERSON>'s best friend.
    <PERSON> Weasley is also <PERSON>'s best friend and Hermione's husband. Ginny is <PERSON>'s sister.
    """
    
    print("🔄 生成哈利波特家族知识图谱...")
    
    try:
        graph1 = kg.generate(input_data=text1, context="Harry Potter family relationships")
        
        print("✅ 成功!")
        print(f"📊 实体: {len(graph1.entities)} 个")
        print(f"🔗 关系: {len(graph1.relations)} 个")
        
        # 生成可视化
        visualize(graph1, "harry_potter_family.html")
        print("🎨 可视化已保存到: harry_potter_family.html")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    # 示例2: 科技公司关系
    print("\n📝 示例2: 科技公司关系")
    text2 = """
    Apple is a technology company founded by Steve Jobs, Steve Wozniak, and Ronald Wayne.
    Microsoft was founded by Bill Gates and Paul Allen. Google was founded by Larry Page and Sergey Brin.
    Facebook was founded by Mark Zuckerberg. Tesla was founded by Elon Musk. Amazon was founded by Jeff Bezos.
    Apple competes with Microsoft in the computer market. Google competes with Microsoft in cloud services.
    """
    
    print("🔄 生成科技公司知识图谱...")
    
    try:
        graph2 = kg.generate(
            input_data=text2, 
            context="Technology companies and founders",
            cluster=True  # 启用聚类
        )
        
        print("✅ 成功!")
        print(f"📊 实体: {len(graph2.entities)} 个")
        print(f"🔗 关系: {len(graph2.relations)} 个")
        
        # 生成可视化
        visualize(graph2, "tech_companies.html")
        print("🎨 可视化已保存到: tech_companies.html")
        
        # 显示聚类结果
        if graph2.entity_clusters:
            print("\n🎯 实体聚类:")
            for rep, members in graph2.entity_clusters.items():
                if len(members) > 1:
                    print(f"  • {rep}: {', '.join(members)}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    # 示例3: 学术概念关系
    print("\n📝 示例3: 人工智能学术概念")
    text3 = """
    Artificial Intelligence is a branch of computer science that aims to create intelligent machines.
    Machine Learning is a subset of AI that enables computers to learn without being explicitly programmed.
    Deep Learning is a subset of Machine Learning that uses neural networks with multiple layers.
    Natural Language Processing is a field of AI that focuses on the interaction between computers and human language.
    Computer Vision is another important area of AI that enables machines to interpret visual information.
    Reinforcement Learning is a type of Machine Learning where agents learn through interaction with an environment.
    """
    
    print("🔄 生成AI概念知识图谱...")
    
    try:
        graph3 = kg.generate(
            input_data=text3,
            context="Artificial Intelligence academic concepts",
            cluster=True
        )
        
        print("✅ 成功!")
        print(f"📊 实体: {len(graph3.entities)} 个")
        print(f"🔗 关系: {len(graph3.relations)} 个")
        
        # 生成可视化
        visualize(graph3, "ai_concepts.html")
        print("🎨 可视化已保存到: ai_concepts.html")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    print("\n" + "=" * 40)
    print("🎉 所有演示完成!")
    print("\n📁 生成的可视化文件:")
    print("  • harry_potter_family.html - 哈利波特家族关系图")
    print("  • tech_companies.html - 科技公司关系图")
    print("  • ai_concepts.html - AI概念关系图")
    print("\n💡 使用方法:")
    print("  • 双击HTML文件在浏览器中打开")
    print("  • 可以拖拽节点、缩放、悬停查看详情")
    print("  • 不同颜色代表不同的聚类")

if __name__ == "__main__":
    main()
