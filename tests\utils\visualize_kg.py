#!/usr/bin/env python3
"""
Interactive visualization of knowledge graphs generated by kg-gen.
Accepts a JSON file representing a kg_gen.models.Graph object.
"""

import random
import sys
import math
from collections import defaultdict
from src.kg_gen.models import Graph
from pyvis.network import Network


def random_color():
    """Generate a random pastel color in hex."""
    # Generate slightly lighter pastel colors
    r = int(127 + random.random() * 128)
    g = int(127 + random.random() * 128)
    b = int(127 + random.random() * 128)
    return f"#{r:02x}{g:02x}{b:02x}"


def graph_to_pyvis_data(graph: Graph):
    """Convert a Graph object into pyvis-compatible node and edge lists."""
    nodes = []  # list of dicts: {id, label, title, color, size, ...}
    edges = []  # list of dicts: {source, target, label, title, color, ...}

    default_node_color = "#ADD8E6"  # Light blue
    default_edge_color = "#A9A9A9"  # Dark gray
    cluster_colors = {}
    edge_cluster_colors = {}
    node_degrees = defaultdict(int)  # To calculate node sizes

    # Pre-calculate degrees
    for subj, _, obj in graph.relations:
        node_degrees[subj] += 1
        node_degrees[obj] += 1

    # --- Process Nodes ---
    if graph.entity_clusters:
        cluster_colors = {rep: random_color() for rep in graph.entity_clusters}
        # Ensure all entities are mapped, even representatives if not explicitly listed as members
        member_to_rep = {}
        for rep, members in graph.entity_clusters.items():
            member_to_rep[rep] = rep  # Map representative to itself
            for member in members:
                member_to_rep[member] = rep

        for entity in graph.entities:
            cluster_rep = member_to_rep.get(entity)
            is_representative = entity == cluster_rep
            degree = node_degrees.get(entity, 0)
            # Scale size logarithmically with degree, minimum size 10, max 40
            size = min(max(10 + 5 * math.log(1 + degree), 10), 40)

            if cluster_rep:
                color = cluster_colors.get(cluster_rep, default_node_color)
                cluster_members = graph.entity_clusters.get(
                    cluster_rep, {cluster_rep}
                )  # Handle case where rep might not be key
                title = (
                    f"<b>Entity:</b> {entity}<br>"
                    f"<b>Cluster Rep:</b> {cluster_rep}<br>"
                    f"<b>Members:</b> {', '.join(sorted(cluster_members))}<br>"
                    f"<b>Degree:</b> {degree}"
                )
                # Make representatives slightly larger and distinct shape
                node_shape = "star" if is_representative else "dot"
                node_size = size * 1.2 if is_representative else size
            else:
                # Entity not part of any cluster (shouldn't happen if clustering included all)
                color = default_node_color
                title = f"<b>Entity:</b> {entity}<br><i>(Not Clustered)</i><br><b>Degree:</b> {degree}"
                node_shape = "dot"
                node_size = size

            nodes.append(
                {
                    "id": entity,
                    "label": entity,
                    "title": title,
                    "color": color,
                    "size": node_size,
                    "shape": node_shape,
                }
            )
    else:
        # No clustering info
        for entity in graph.entities:
            degree = node_degrees.get(entity, 0)
            size = min(max(10 + 5 * math.log(1 + degree), 10), 40)
            title = f"<b>Entity:</b> {entity}<br><b>Degree:</b> {degree}"
            nodes.append(
                {
                    "id": entity,
                    "label": entity,
                    "title": title,
                    "color": default_node_color,
                    "size": size,
                    "shape": "dot",
                }
            )

    # --- Process Edges ---
    if graph.edge_clusters:
        edge_cluster_colors = {rep: random_color() for rep in graph.edge_clusters}
        edge_member_to_rep = {}
        for rep, members in graph.edge_clusters.items():
            edge_member_to_rep[rep] = rep  # Map representative to itself
            for member in members:
                edge_member_to_rep[member] = rep

        for subj, pred, obj in graph.relations:
            cluster_rep = edge_member_to_rep.get(pred)
            if cluster_rep:
                color = edge_cluster_colors.get(cluster_rep, default_edge_color)
                cluster_members = graph.edge_clusters.get(cluster_rep, {cluster_rep})
                title = (
                    f"<b>Relation:</b> {pred}<br>"
                    f"<b>Cluster Rep:</b> {cluster_rep}<br>"
                    f"<b>Members:</b> {', '.join(sorted(cluster_members))}"
                )
                # Use representative label for clustered edges for clarity? Or original? Let's use original.
                label = pred
            else:
                color = default_edge_color
                title = f"<b>Relation:</b> {pred}<br><i>(Not Clustered)</i>"
                label = pred

            edges.append(
                {
                    "source": subj,
                    "target": obj,
                    "label": label,
                    "title": title,
                    "color": color,
                }
            )
    else:
        # No edge clustering, assign colors based on unique predicates
        unique_preds = graph.edges
        pred_colors = {pred: random_color() for pred in unique_preds}
        for subj, pred, obj in graph.relations:
            color = pred_colors.get(pred, default_edge_color)
            title = f"<b>Relation:</b> {pred}"
            edges.append(
                {
                    "source": subj,
                    "target": obj,
                    "label": pred,
                    "title": title,
                    "color": color,
                }
            )

    return nodes, edges


def visualize(graph: Graph, output_path: str):
    """Render the graph object to an interactive HTML file using pyvis."""
    if not graph or not graph.entities or not graph.relations:
        sys.stderr.write("Error: Cannot visualize empty or invalid graph\n")
        sys.exit(1)

    nodes, edges = graph_to_pyvis_data(graph)

    try:
        net = Network(
            height="800px",
            width="100%",
            directed=True,
            notebook=False,
            cdn_resources="remote",
        )
    except Exception as e:
        sys.stderr.write(f"Error initializing Network: {e}\n")
        sys.exit(1)

    # Enhanced aesthetics and physics options - Removed comments for JSON compatibility
    net.set_options(
        """
        var options = {
          "nodes": {
            "font": {"size": 14, "face": "tahoma"},
            "borderWidth": 1,
            "borderWidthSelected": 2
          },
          "edges": {
            "arrows": {"to": {"enabled": true, "scaleFactor": 0.6}},
            "color": {"inherit": false},
            "smooth": {"type": "dynamic", "roundness": 0.2},
            "font": {"size": 11, "face": "tahoma", "align": "middle", "strokeWidth": 2, "strokeColor": "#ffffff"},
            "width": 1.5,
            "hoverWidth": 0.5,
            "selectionWidth": 1
          },
          "physics": {
            "enabled": true,
            "barnesHut": {
              "gravitationalConstant": -25000,
              "centralGravity": 0.1,
              "springLength": 120,
              "springConstant": 0.05,
              "damping": 0.15,
              "avoidOverlap": 0.5
            },
            "maxVelocity": 50,
            "minVelocity": 0.5,
            "solver": "barnesHut",
            "stabilization": {
              "enabled": true,
              "iterations": 1000,
              "updateInterval": 50,
              "onlyDynamicEdges": false,
              "fit": true
            },
            "timestep": 0.5,
            "adaptiveTimestep": true
          },
          "interaction": {
            "dragNodes": true,
            "dragView": true,
            "hideEdgesOnDrag": false,
            "hideNodesOnDrag": false,
            "hover": true,
            "hoverConnectedEdges": true,
            "keyboard": {"enabled": true},
            "multiselect": true,
            "navigationButtons": true,
            "selectable": true,
            "selectConnectedEdges": true,
            "tooltipDelay": 200,
            "zoomView": true
          },
          "manipulation": {
             "enabled": false
          }
        }
        """
    )

    # Add nodes and edges with specific properties
    try:
        for n in nodes:
            net.add_node(
                n["id"],
                label=n["label"],
                title=n.get("title"),  # HTML title for hover tooltip
                color=n.get("color"),
                size=n.get("size"),
                shape=n.get("shape", "dot"),  # Use shape if defined
            )

        for e in edges:
            net.add_edge(
                e["source"],
                e["target"],
                label=e.get("label"),
                title=e.get("title"),  # HTML title for hover tooltip
                color=e.get("color"),
            )

        # Save the graph
        net.write_html(output_path)
        print(f"Successfully generated visualization: {output_path}")
    except Exception as e:
        sys.stderr.write(f"Error generating HTML file with pyvis: {e}\n")
        sys.stderr.write(
            "Please check that the output directory exists and is writable\n"
        )
        sys.exit(1)
