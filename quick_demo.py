#!/usr/bin/env python3
"""
kg-gen 快速演示脚本
简单快速的知识图谱生成示例
"""

import os
import sys
from dotenv import load_dotenv

# 设置代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kg_gen import KGGen

def main():
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 错误: 请在.env文件中设置OPENAI_API_KEY")
        return
    
    print("🚀 kg-gen 快速演示")
    print("=" * 40)
    
    # 初始化KGGen（使用更快的模型）
    kg = KGGen(
        model="openai/gpt-4o-mini",
        temperature=0.0,
        api_key=api_key
    )
    
    # 简单示例
    print("\n📝 分析简单文本:")
    text = "Linda is Josh's mother. Ben is Josh's brother. Andrew is Josh's father."
    print(f"输入: {text}")
    
    print("\n🔄 生成知识图谱...")
    
    try:
        graph = kg.generate(input_data=text)
        
        print("✅ 成功!")
        print(f"📊 找到 {len(graph.entities)} 个实体")
        print(f"🔗 找到 {len(graph.relations)} 个关系")
        
        print("\n🎯 实体:")
        for entity in sorted(graph.entities):
            print(f"  • {entity}")
        
        print("\n🔗 关系:")
        for relation in graph.relations:
            subject, predicate, obj = relation
            print(f"  • {subject} → {predicate} → {obj}")
            
        print("\n🎉 演示完成!")
        print("\n💡 你可以:")
        print("  • 修改 quick_demo.py 中的文本")
        print("  • 运行 python demo.py 查看更多示例")
        print("  • 查看 README.md 了解完整功能")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n🔧 可能的解决方案:")
        print("  • 检查网络连接")
        print("  • 确认API密钥正确")
        print("  • 稍后重试")

if __name__ == "__main__":
    main()
