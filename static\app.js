// KG-Gen Web应用前端JavaScript

let currentGraphId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload();
    initializeTextForm();
    initializeForms();
});

// 初始化文件上传功能
function initializeFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');

    // 点击上传区域选择文件
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileSelect(file);
        }
    });

    // 拖拽事件
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            fileInput.files = files;
            handleFileSelect(file);
        }
    });

    function handleFileSelect(file) {
        // 检查文件类型
        const allowedTypes = ['text/plain', 'text/csv', 'application/vnd.ms-excel', 
                             'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             'application/json'];
        const allowedExtensions = ['.txt', '.csv', '.xlsx', '.xls', '.json'];
        
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedExtensions.includes(fileExtension)) {
            showError('不支持的文件格式。请选择 .txt, .csv, .xlsx, .xls 或 .json 文件。');
            return;
        }

        // 检查文件大小 (16MB)
        if (file.size > 16 * 1024 * 1024) {
            showError('文件大小超过16MB限制。');
            return;
        }

        // 显示文件信息
        fileName.textContent = `${file.name} (${formatFileSize(file.size)})`;
        fileInfo.style.display = 'block';
        uploadBtn.disabled = false;
    }
}

// 初始化文本表单
function initializeTextForm() {
    const textForm = document.getElementById('textForm');
    
    textForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const textContent = document.getElementById('textContent').value.trim();
        if (!textContent) {
            showError('请输入文本内容');
            return;
        }

        const formData = {
            text: textContent,
            model: document.getElementById('textModel').value,
            chunk_size: parseInt(document.getElementById('textChunkSize').value) || null,
            cluster: document.getElementById('textCluster').checked,
            context: document.getElementById('textContext').value.trim()
        };

        submitTextData(formData);
    });
}

// 初始化表单提交
function initializeForms() {
    const uploadForm = document.getElementById('uploadForm');
    
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('fileInput');
        if (!fileInput.files[0]) {
            showError('请选择文件');
            return;
        }

        const formData = new FormData(uploadForm);
        submitFileData(formData);
    });

    // 结果按钮事件
    document.getElementById('viewGraphBtn').addEventListener('click', function() {
        if (currentGraphId) {
            const url = `/static/graphs/graph_${currentGraphId}.html`;
            window.open(url, '_blank');
        }
    });

    document.getElementById('downloadBtn').addEventListener('click', function() {
        if (currentGraphId) {
            const url = `/download/${currentGraphId}`;
            window.open(url, '_blank');
        }
    });
}

// 提交文本数据
async function submitTextData(data) {
    const submitBtn = document.querySelector('#textForm button[type="submit"]');
    setLoadingState(submitBtn, true);
    hideError();
    hideResult();

    try {
        const response = await fetch('/text_input', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            displayResult(result);
        } else {
            showError(result.error || '处理失败');
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    } finally {
        setLoadingState(submitBtn, false);
    }
}

// 提交文件数据
async function submitFileData(formData) {
    const submitBtn = document.getElementById('uploadBtn');
    setLoadingState(submitBtn, true);
    hideError();
    hideResult();

    try {
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            displayResult(result);
        } else {
            showError(result.error || '处理失败');
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    } finally {
        setLoadingState(submitBtn, false);
    }
}

// 显示结果
function displayResult(result) {
    currentGraphId = result.graph_id;
    
    // 更新统计信息
    document.getElementById('entitiesCount').textContent = result.stats.entities_count;
    document.getElementById('relationsCount').textContent = result.stats.relations_count;
    document.getElementById('edgesCount').textContent = result.stats.edges_count;
    
    // 显示实体
    const entitiesList = document.getElementById('entitiesList');
    entitiesList.innerHTML = '';
    result.entities.forEach(entity => {
        const tag = document.createElement('span');
        tag.className = 'entity-tag';
        tag.textContent = entity;
        entitiesList.appendChild(tag);
    });
    
    // 显示关系
    const relationsList = document.getElementById('relationsList');
    relationsList.innerHTML = '';
    result.relations.forEach(relation => {
        const item = document.createElement('div');
        item.className = 'relation-item';
        item.innerHTML = `
            <strong>${relation.subject}</strong> 
            <i class="fas fa-arrow-right text-muted"></i> 
            <em>${relation.predicate}</em> 
            <i class="fas fa-arrow-right text-muted"></i> 
            <strong>${relation.object}</strong>
        `;
        relationsList.appendChild(item);
    });
    
    // 显示聚类信息
    const clustersInfo = document.getElementById('clustersInfo');
    const clustersList = document.getElementById('clustersList');
    
    if (result.entity_clusters && Object.keys(result.entity_clusters).length > 0) {
        clustersInfo.style.display = 'block';
        clustersList.innerHTML = '';
        
        Object.entries(result.entity_clusters).forEach(([representative, members]) => {
            if (members.length > 1) {
                const clusterDiv = document.createElement('div');
                clusterDiv.className = 'mb-2';
                clusterDiv.innerHTML = `
                    <strong>${representative}:</strong> 
                    ${members.map(member => `<span class="entity-tag">${member}</span>`).join(' ')}
                `;
                clustersList.appendChild(clusterDiv);
            }
        });
    } else {
        clustersInfo.style.display = 'none';
    }
    
    // 显示结果卡片
    document.getElementById('resultCard').style.display = 'block';
    
    // 滚动到结果区域
    document.getElementById('resultCard').scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
    });
}

// 设置加载状态
function setLoadingState(button, loading) {
    const loadingSpan = button.querySelector('.loading');
    const notLoadingSpan = button.querySelector('.not-loading');
    
    if (loading) {
        loadingSpan.style.display = 'inline';
        notLoadingSpan.style.display = 'none';
        button.disabled = true;
    } else {
        loadingSpan.style.display = 'none';
        notLoadingSpan.style.display = 'inline';
        button.disabled = false;
    }
}

// 显示错误
function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    
    // 滚动到错误提示
    errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // 5秒后自动隐藏
    setTimeout(hideError, 5000);
}

// 隐藏错误
function hideError() {
    document.getElementById('errorAlert').style.display = 'none';
}

// 隐藏结果
function hideResult() {
    document.getElementById('resultCard').style.display = 'none';
}

// 清除文件选择
function clearFile() {
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('uploadBtn').disabled = true;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
