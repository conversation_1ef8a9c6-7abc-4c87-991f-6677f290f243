#!/usr/bin/env python3
"""
kg-gen Web应用
支持文件上传和知识图谱生成的Web界面
"""

import os
import sys
import json
import pandas as pd
from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import tempfile
import uuid

# 设置代理
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:7890'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:7890'

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kg_gen import KGGen
from tests.utils.visualize_kg import visualize

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.secret_key = 'kg-gen-secret-key-2024'

# 配置文件上传
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'csv', 'xlsx', 'xls', 'json'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('static/graphs', exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def read_file_content(filepath):
    """读取不同格式文件的内容"""
    try:
        file_ext = filepath.rsplit('.', 1)[1].lower()
        
        if file_ext == 'txt':
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        
        elif file_ext == 'csv':
            df = pd.read_csv(filepath)
            return df.to_string()
        
        elif file_ext in ['xlsx', 'xls']:
            df = pd.read_excel(filepath)
            return df.to_string()
        
        elif file_ext == 'json':
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return json.dumps(data, ensure_ascii=False, indent=2)
        
        else:
            return None
            
    except Exception as e:
        print(f"读取文件错误: {e}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 保存文件
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)
        
        # 读取文件内容
        content = read_file_content(filepath)
        if content is None:
            return jsonify({'error': '无法读取文件内容'}), 400
        
        # 获取参数
        model = request.form.get('model', 'openai/gpt-4o-mini')
        chunk_size = request.form.get('chunk_size', '')
        enable_cluster = request.form.get('cluster') == 'on'
        context = request.form.get('context', '')
        
        # 检查API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return jsonify({'error': '未配置OpenAI API密钥'}), 500
        
        # 初始化KGGen
        kg = KGGen(
            model=model,
            temperature=0.0,
            api_key=api_key
        )
        
        # 生成知识图谱
        generate_params = {
            'input_data': content,
            'cluster': enable_cluster,
            'context': context
        }
        
        if chunk_size and chunk_size.isdigit():
            generate_params['chunk_size'] = int(chunk_size)
        
        graph = kg.generate(**generate_params)
        
        # 生成可视化
        graph_id = str(uuid.uuid4())
        html_filename = f"graph_{graph_id}.html"
        html_path = os.path.join('static/graphs', html_filename)
        visualize(graph, html_path)
        
        # 准备返回数据
        result = {
            'success': True,
            'graph_id': graph_id,
            'stats': {
                'entities_count': len(graph.entities),
                'relations_count': len(graph.relations),
                'edges_count': len(graph.edges)
            },
            'entities': list(graph.entities),
            'relations': [{'subject': r[0], 'predicate': r[1], 'object': r[2]} for r in graph.relations],
            'visualization_url': f'/static/graphs/{html_filename}'
        }
        
        # 添加聚类信息
        if graph.entity_clusters:
            result['entity_clusters'] = {k: list(v) for k, v in graph.entity_clusters.items()}
        if graph.edge_clusters:
            result['edge_clusters'] = {k: list(v) for k, v in graph.edge_clusters.items()}
        
        # 清理上传的文件
        os.remove(filepath)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/text_input', methods=['POST'])
def process_text():
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'error': '请输入文本内容'}), 400
        
        # 获取参数
        model = data.get('model', 'openai/gpt-4o-mini')
        chunk_size = data.get('chunk_size')
        enable_cluster = data.get('cluster', False)
        context = data.get('context', '')
        
        # 检查API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return jsonify({'error': '未配置OpenAI API密钥'}), 500
        
        # 初始化KGGen
        kg = KGGen(
            model=model,
            temperature=0.0,
            api_key=api_key
        )
        
        # 生成知识图谱
        generate_params = {
            'input_data': text,
            'cluster': enable_cluster,
            'context': context
        }
        
        if chunk_size and isinstance(chunk_size, int) and chunk_size > 0:
            generate_params['chunk_size'] = chunk_size
        
        graph = kg.generate(**generate_params)
        
        # 生成可视化
        graph_id = str(uuid.uuid4())
        html_filename = f"graph_{graph_id}.html"
        html_path = os.path.join('static/graphs', html_filename)
        visualize(graph, html_path)
        
        # 准备返回数据
        result = {
            'success': True,
            'graph_id': graph_id,
            'stats': {
                'entities_count': len(graph.entities),
                'relations_count': len(graph.relations),
                'edges_count': len(graph.edges)
            },
            'entities': list(graph.entities),
            'relations': [{'subject': r[0], 'predicate': r[1], 'object': r[2]} for r in graph.relations],
            'visualization_url': f'/static/graphs/{html_filename}'
        }
        
        # 添加聚类信息
        if graph.entity_clusters:
            result['entity_clusters'] = {k: list(v) for k, v in graph.entity_clusters.items()}
        if graph.edge_clusters:
            result['edge_clusters'] = {k: list(v) for k, v in graph.edge_clusters.items()}
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'处理文本时出错: {str(e)}'}), 500

@app.route('/download/<graph_id>')
def download_graph(graph_id):
    try:
        html_filename = f"graph_{graph_id}.html"
        html_path = os.path.join('static/graphs', html_filename)
        
        if os.path.exists(html_path):
            return send_file(html_path, as_attachment=True, download_name=f"knowledge_graph_{graph_id}.html")
        else:
            return jsonify({'error': '文件不存在'}), 404
            
    except Exception as e:
        return jsonify({'error': f'下载文件时出错: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 启动 kg-gen Web应用")
    print("📱 访问地址: http://localhost:5000")
    print("🔧 支持的文件格式: txt, csv, xlsx, xls, json")
    print("💡 确保已设置OPENAI_API_KEY环境变量")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
