#!/usr/bin/env python3
"""
kg-gen 演示脚本
展示如何使用kg-gen从文本生成知识图谱
"""

import os
import sys
from dotenv import load_dotenv

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kg_gen import KGGen

def main():
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 错误: 请在.env文件中设置OPENAI_API_KEY")
        return
    
    print("🚀 启动 kg-gen 知识图谱生成演示")
    print("=" * 50)
    
    # 初始化KGGen
    kg = KGGen(
        model="openai/gpt-4o",
        temperature=0.0,
        api_key=api_key
    )
    
    # 示例1: 简单家庭关系
    print("\n📝 示例1: 家庭关系分析")
    text1 = "哈利有两个父母 - 他的爸爸詹姆斯·波特和他的妈妈莉莉·波特。哈利和他的妻子金妮有三个孩子：他们的大儿子詹姆斯·小天狼星，另一个儿子阿不思，还有他们的女儿莉莉·卢娜。"
    
    print(f"输入文本: {text1}")
    print("\n🔄 正在生成知识图谱...")
    
    try:
        graph1 = kg.generate(input_data=text1)
        
        print("✅ 生成成功!")
        print(f"📊 实体数量: {len(graph1.entities)}")
        print(f"🔗 关系数量: {len(graph1.relations)}")
        print(f"🏷️ 关系类型: {len(graph1.edges)}")
        
        print("\n🎯 提取的实体:")
        for entity in sorted(graph1.entities):
            print(f"  • {entity}")
        
        print("\n🔗 提取的关系:")
        for relation in graph1.relations:
            subject, predicate, obj = relation
            print(f"  • {subject} → {predicate} → {obj}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    # 示例2: 对话分析
    print("\n" + "=" * 50)
    print("📝 示例2: 对话分析")
    
    messages = [
        {"role": "user", "content": "法国的首都是什么？"},
        {"role": "assistant", "content": "法国的首都是巴黎。巴黎是法国最大的城市，也是政治和文化中心。"}
    ]
    
    print("输入对话:")
    for msg in messages:
        print(f"  {msg['role']}: {msg['content']}")
    
    print("\n🔄 正在分析对话...")
    
    try:
        graph2 = kg.generate(input_data=messages)
        
        print("✅ 分析成功!")
        print(f"📊 实体数量: {len(graph2.entities)}")
        print(f"🔗 关系数量: {len(graph2.relations)}")
        
        print("\n🎯 提取的实体:")
        for entity in sorted(graph2.entities):
            print(f"  • {entity}")
        
        print("\n🔗 提取的关系:")
        for relation in graph2.relations:
            subject, predicate, obj = relation
            print(f"  • {subject} → {predicate} → {obj}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    # 示例3: 技术文档分析（带分块和聚类）
    print("\n" + "=" * 50)
    print("📝 示例3: 技术文档分析（分块+聚类）")
    
    tech_text = """
    人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
    机器学习是人工智能的一个子集，它使计算机能够从数据中学习而无需明确编程。
    深度学习是机器学习的一个子集，使用具有多层的神经网络来模拟人脑的工作方式。
    神经网络是受生物神经网络启发的计算模型，由相互连接的节点（神经元）组成。
    自然语言处理（NLP）是AI的一个领域，专注于计算机与人类语言之间的交互。
    计算机视觉是AI的另一个重要领域，使机器能够解释和理解视觉信息。
    """
    
    print(f"输入文本长度: {len(tech_text)} 字符")
    print("\n🔄 正在进行分块处理和聚类分析...")
    
    try:
        graph3 = kg.generate(
            input_data=tech_text,
            chunk_size=200,  # 小分块用于演示
            cluster=True,    # 启用聚类
            context="人工智能技术文档"
        )
        
        print("✅ 分析成功!")
        print(f"📊 实体数量: {len(graph3.entities)}")
        print(f"🔗 关系数量: {len(graph3.relations)}")
        print(f"🏷️ 关系类型: {len(graph3.edges)}")
        
        print("\n🎯 提取的实体:")
        for entity in sorted(graph3.entities):
            print(f"  • {entity}")
        
        print("\n🔗 提取的关系:")
        for relation in graph3.relations:
            subject, predicate, obj = relation
            print(f"  • {subject} → {predicate} → {obj}")
        
        # 显示聚类结果
        if graph3.entity_clusters:
            print("\n🎯 实体聚类结果:")
            for rep, members in graph3.entity_clusters.items():
                if len(members) > 1:
                    print(f"  • {rep}: {', '.join(members)}")
        
        if graph3.edge_clusters:
            print("\n🔗 关系聚类结果:")
            for rep, members in graph3.edge_clusters.items():
                if len(members) > 1:
                    print(f"  • {rep}: {', '.join(members)}")
                    
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    print("\n" + "=" * 50)
    print("🎉 演示完成！")
    print("\n💡 提示:")
    print("  • 你可以修改demo.py中的文本来测试不同的内容")
    print("  • 支持多种模型：openai/gpt-4o, anthropic/claude-3-5-sonnet等")
    print("  • 可以调整chunk_size和cluster参数来优化结果")
    print("  • 查看README.md了解更多功能")

if __name__ == "__main__":
    main()
