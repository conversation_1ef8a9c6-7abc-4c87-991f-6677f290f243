<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#c8f3f2", "id": "visual information", "label": "visual information", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e visual information\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e visual information\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e visual information\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#dba088", "id": "neural networks", "label": "neural networks", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e neural networks\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e neural networks\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e neural networks\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#af9788", "id": "computers", "label": "computers", "shape": "star", "size": 18.59167373200866, "title": "\u003cb\u003eEntity:\u003c/b\u003e computers\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e computers\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e computers\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#a4f4d3", "id": "computer science", "label": "computer science", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e computer science\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e computer science\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e computer science\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#f0f6f7", "id": "Natural Language Processing", "label": "Natural Language Processing", "shape": "star", "size": 20.317766166719345, "title": "\u003cb\u003eEntity:\u003c/b\u003e Natural Language Processing\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e Natural Language Processing\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e Natural Language Processing\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 3"}, {"color": "#c0c3e9", "id": "human language", "label": "human language", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e human language\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e human language\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e human language\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#b2c5c6", "id": "Computer Vision", "label": "Computer Vision", "shape": "star", "size": 18.59167373200866, "title": "\u003cb\u003eEntity:\u003c/b\u003e Computer Vision\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e Computer Vision\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e Computer Vision\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#b4a8f1", "id": "agents", "label": "agents", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e agents\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e agents\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e agents\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#e98b95", "id": "AI", "label": "AI", "shape": "star", "size": 21.6566274746046, "title": "\u003cb\u003eEntity:\u003c/b\u003e AI\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e AI\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e AI, Artificial Intelligence, intelligent machines\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 4"}, {"color": "#9cca8f", "id": "Deep Learning", "label": "Deep Learning", "shape": "star", "size": 18.59167373200866, "title": "\u003cb\u003eEntity:\u003c/b\u003e Deep Learning\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e Deep Learning\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e Deep Learning\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#a1f599", "id": "Reinforcement Learning", "label": "Reinforcement Learning", "shape": "star", "size": 20.317766166719345, "title": "\u003cb\u003eEntity:\u003c/b\u003e Reinforcement Learning\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e Reinforcement Learning\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e Reinforcement Learning\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 3"}, {"color": "#9bbef6", "id": "Machine Learning", "label": "Machine Learning", "shape": "star", "size": 21.6566274746046, "title": "\u003cb\u003eEntity:\u003c/b\u003e Machine Learning\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e Machine Learning\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e Machine Learning\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 4"}, {"color": "#be86b9", "id": "environment", "label": "environment", "shape": "star", "size": 16.158883083359672, "title": "\u003cb\u003eEntity:\u003c/b\u003e environment\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e environment\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e environment\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#c8e5d3", "from": "AI", "label": "is a branch of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is a branch of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is a branch of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is a branch of", "to": "computer science"}, {"arrows": "to", "color": "#fd88e7", "from": "Deep Learning", "label": "uses", "title": "\u003cb\u003eRelation:\u003c/b\u003e uses\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e uses\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e uses", "to": "neural networks"}, {"arrows": "to", "color": "#c691f6", "from": "Machine Learning", "label": "enables", "title": "\u003cb\u003eRelation:\u003c/b\u003e enables\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e enables\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e enables", "to": "computers"}, {"arrows": "to", "color": "#a8cb8f", "from": "Natural Language Processing", "label": "focuses on the interaction between", "title": "\u003cb\u003eRelation:\u003c/b\u003e focuses on the interaction between\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e focuses on the interaction between\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e focuses on the interaction between", "to": "computers"}, {"arrows": "to", "color": "#96bb96", "from": "Reinforcement Learning", "label": "involves", "title": "\u003cb\u003eRelation:\u003c/b\u003e involves\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e involves\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e involves", "to": "agents"}, {"arrows": "to", "color": "#fbefc6", "from": "Reinforcement Learning", "label": "involves interaction with", "title": "\u003cb\u003eRelation:\u003c/b\u003e involves interaction with\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e involves interaction with\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e involves interaction with", "to": "environment"}, {"arrows": "to", "color": "#c386bb", "from": "Natural Language Processing", "label": "is a field of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is a field of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is a field of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is a field of", "to": "AI"}, {"arrows": "to", "color": "#f19af0", "from": "Deep Learning", "label": "is a subset of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is a subset of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is a subset of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is a subset of", "to": "Machine Learning"}, {"arrows": "to", "color": "#f19af0", "from": "Machine Learning", "label": "is a subset of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is a subset of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is a subset of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is a subset of", "to": "AI"}, {"arrows": "to", "color": "#de8f98", "from": "Reinforcement Learning", "label": "is a type of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is a type of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is a type of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is a type of", "to": "Machine Learning"}, {"arrows": "to", "color": "#a0a4ee", "from": "Computer Vision", "label": "is an area of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is an area of\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e is an area of\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e is an area of", "to": "AI"}, {"arrows": "to", "color": "#c4acf4", "from": "Natural Language Processing", "label": "focuses on the interaction with", "title": "\u003cb\u003eRelation:\u003c/b\u003e focuses on the interaction with\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e focuses on the interaction with\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e focuses on the interaction with", "to": "human language"}, {"arrows": "to", "color": "#fccbe4", "from": "Computer Vision", "label": "enables machines to interpret", "title": "\u003cb\u003eRelation:\u003c/b\u003e enables machines to interpret\u003cbr\u003e\u003cb\u003eCluster Rep:\u003c/b\u003e enables machines to interpret\u003cbr\u003e\u003cb\u003eMembers:\u003c/b\u003e enables machines to interpret", "to": "visual information"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"nodes": {"font": {"size": 14, "face": "tahoma"}, "borderWidth": 1, "borderWidthSelected": 2}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 0.6}}, "color": {"inherit": false}, "smooth": {"type": "dynamic", "roundness": 0.2}, "font": {"size": 11, "face": "tahoma", "align": "middle", "strokeWidth": 2, "strokeColor": "#ffffff"}, "width": 1.5, "hoverWidth": 0.5, "selectionWidth": 1}, "physics": {"enabled": true, "barnesHut": {"gravitationalConstant": -25000, "centralGravity": 0.1, "springLength": 120, "springConstant": 0.05, "damping": 0.15, "avoidOverlap": 0.5}, "maxVelocity": 50, "minVelocity": 0.5, "solver": "barnesHut", "stabilization": {"enabled": true, "iterations": 1000, "updateInterval": 50, "onlyDynamicEdges": false, "fit": true}, "timestep": 0.5, "adaptiveTimestep": true}, "interaction": {"dragNodes": true, "dragView": true, "hideEdgesOnDrag": false, "hideNodesOnDrag": false, "hover": true, "hoverConnectedEdges": true, "keyboard": {"enabled": true}, "multiselect": true, "navigationButtons": true, "selectable": true, "selectConnectedEdges": true, "tooltipDelay": 200, "zoomView": true}, "manipulation": {"enabled": false}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>