from src.kg_gen import KGGen
import os
from dotenv import load_dotenv

from tests.utils.visualize_kg import visualize

if __name__ == "__main__":
  # Load environment variables
  load_dotenv()
  
  # Example usage
  kg = KGGen(api_key=os.getenv("OPENAI_API_KEY"))
  
  # Generate a simple graph
  text = "<PERSON> has two parents - his dad <PERSON> and his mom <PERSON>. <PERSON> and his wife <PERSON><PERSON><PERSON> have three kids together: their oldest son <PERSON>, their other son <PERSON><PERSON>, and their daughter <PERSON>."
  
  graph = kg.generate(
    input_data=text,
    model="openai/gpt-4o"
  )

  visualize(graph, "tests/test_basic.html")

  print(graph)