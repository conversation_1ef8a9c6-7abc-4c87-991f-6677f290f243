<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ADD8E6", "id": "\u4fc4\u56fd\u95f4\u8c0d", "label": "\u4fc4\u56fd\u95f4\u8c0d", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u4fc4\u56fd\u95f4\u8c0d\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u8d6b\u5c14\u66fc\u00b7\u7231\u56e0\u65af\u5766", "label": "\u8d6b\u5c14\u66fc\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u8d6b\u5c14\u66fc\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u7f57\u7d20\u2014\u7231\u56e0\u65af\u5766\u5ba3\u8a00", "label": "\u7f57\u7d20\u2014\u7231\u56e0\u65af\u5766\u5ba3\u8a00", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7f57\u7d20\u2014\u7231\u56e0\u65af\u5766\u5ba3\u8a00\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u5149\u7535\u6548\u5e94", "label": "\u5149\u7535\u6548\u5e94", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u5149\u7535\u6548\u5e94\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u739b\u683c\u4e3d\u5854\u00b7\u79d1\u5ae9\u79d1\u5a03", "label": "\u739b\u683c\u4e3d\u5854\u00b7\u79d1\u5ae9\u79d1\u5a03", "shape": "dot", "size": 15.49306144334055, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u739b\u683c\u4e3d\u5854\u00b7\u79d1\u5ae9\u79d1\u5a03\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#ADD8E6", "id": "\u739b\u8389\u00b7\u6e29\u7279\u52d2", "label": "\u739b\u8389\u00b7\u6e29\u7279\u52d2", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u739b\u8389\u00b7\u6e29\u7279\u52d2\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u5fb7\u56fd", "label": "\u5fb7\u56fd", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u5fb7\u56fd\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u6c49\u65af\u00b7\u7231\u56e0\u65af\u5766", "label": "\u6c49\u65af\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u6c49\u65af\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u7231\u5fb7\u534e\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7231\u5fb7\u534e\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 15.49306144334055, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7231\u5fb7\u534e\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#ADD8E6", "id": "\u5bcc\u5170\u514b\u6797\u00b7\u7f57\u65af\u798f", "label": "\u5bcc\u5170\u514b\u6797\u00b7\u7f57\u65af\u798f", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u5bcc\u5170\u514b\u6797\u00b7\u7f57\u65af\u798f\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u7231\u5c14\u838e\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7231\u5c14\u838e\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7231\u5c14\u838e\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u4fdd\u5229\u5a1c\u00b7\u79d1\u8d6b", "label": "\u4fdd\u5229\u5a1c\u00b7\u79d1\u8d6b", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u4fdd\u5229\u5a1c\u00b7\u79d1\u8d6b\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u7eb3\u7cb9\u5fb7\u56fd", "label": "\u7eb3\u7cb9\u5fb7\u56fd", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7eb3\u7cb9\u5fb7\u56fd\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u8389\u6cfd\u5c14\u00b7\u7231\u56e0\u65af\u5766", "label": "\u8389\u6cfd\u5c14\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u8389\u6cfd\u5c14\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u745e\u58eb", "label": "\u745e\u58eb", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u745e\u58eb\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u7f8e\u56fd", "label": "\u7f8e\u56fd", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7f8e\u56fd\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u66fc\u54c8\u987f\u8ba1\u5212", "label": "\u66fc\u54c8\u987f\u8ba1\u5212", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u66fc\u54c8\u987f\u8ba1\u5212\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u897f\u683c\u8499\u5fb7\u00b7\u4f5b\u6d1b\u4f0a\u5fb7", "label": "\u897f\u683c\u8499\u5fb7\u00b7\u4f5b\u6d1b\u4f0a\u5fb7", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u897f\u683c\u8499\u5fb7\u00b7\u4f5b\u6d1b\u4f0a\u5fb7\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u5e7f\u4e49\u76f8\u5bf9\u8bba", "label": "\u5e7f\u4e49\u76f8\u5bf9\u8bba", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u5e7f\u4e49\u76f8\u5bf9\u8bba\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u91cf\u5b50\u529b\u5b66", "label": "\u91cf\u5b50\u529b\u5b66", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u91cf\u5b50\u529b\u5b66\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u8bfa\u8d1d\u5c14\u7269\u7406\u5b66\u5956", "label": "\u8bfa\u8d1d\u5c14\u7269\u7406\u5b66\u5956", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u8bfa\u8d1d\u5c14\u7269\u7406\u5b66\u5956\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u666e\u6797\u65af\u987f", "label": "\u666e\u6797\u65af\u987f", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u666e\u6797\u65af\u987f\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u9a6c\u5a05\u00b7\u7231\u56e0\u65af\u5766", "label": "\u9a6c\u5a05\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u9a6c\u5a05\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u76f8\u5bf9\u8bba", "label": "\u76f8\u5bf9\u8bba", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u76f8\u5bf9\u8bba\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u666e\u9c81\u58eb\u79d1\u5b66\u9662", "label": "\u666e\u9c81\u58eb\u79d1\u5b66\u9662", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u666e\u9c81\u58eb\u79d1\u5b66\u9662\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u72ed\u4e49\u76f8\u5bf9\u8bba", "label": "\u72ed\u4e49\u76f8\u5bf9\u8bba", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u72ed\u4e49\u76f8\u5bf9\u8bba\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u8d28\u80fd\u7b49\u4ef7", "label": "\u8d28\u80fd\u7b49\u4ef7", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u8d28\u80fd\u7b49\u4ef7\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "shape": "dot", "size": 26.479184330021646, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 26"}, {"color": "#ADD8E6", "id": "\u963f\u9053\u592b\u00b7\u5e0c\u7279\u52d2", "label": "\u963f\u9053\u592b\u00b7\u5e0c\u7279\u52d2", "shape": "dot", "size": 10.0, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u963f\u9053\u592b\u00b7\u5e0c\u7279\u52d2\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 0"}, {"color": "#ADD8E6", "id": "\u4f2f\u7279\u5170\u00b7\u7f57\u7d20", "label": "\u4f2f\u7279\u5170\u00b7\u7f57\u7d20", "shape": "dot", "size": 10.0, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u4f2f\u7279\u5170\u00b7\u7f57\u7d20\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 0"}, {"color": "#ADD8E6", "id": "\u7c73\u5217\u5a03\u00b7\u9a6c\u5229\u5947", "label": "\u7c73\u5217\u5a03\u00b7\u9a6c\u5229\u5947", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e \u7c73\u5217\u5a03\u00b7\u9a6c\u5229\u5947\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#98a6aa", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7b7e\u7f72", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u7b7e\u7f72", "to": "\u7f57\u7d20\u2014\u7231\u56e0\u65af\u5766\u5ba3\u8a00"}, {"arrows": "to", "color": "#f5ddcc", "from": "\u739b\u683c\u4e3d\u5854\u00b7\u79d1\u5ae9\u79d1\u5a03", "label": "\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u662f", "to": "\u4fc4\u56fd\u95f4\u8c0d"}, {"arrows": "to", "color": "#cadaf1", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u513f\u5b50\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u513f\u5b50\u662f", "to": "\u7231\u5fb7\u534e\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#fec0d6", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u62e5\u6709\u56fd\u7c4d", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u62e5\u6709\u56fd\u7c4d", "to": "\u745e\u58eb"}, {"arrows": "to", "color": "#a89f8d", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u53d1\u8868", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u53d1\u8868", "to": "\u5e7f\u4e49\u76f8\u5bf9\u8bba"}, {"arrows": "to", "color": "#a5959f", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7ed3\u5a5a", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u7ed3\u5a5a", "to": "\u7231\u5c14\u838e\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#fec0d6", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u62e5\u6709\u56fd\u7c4d", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u62e5\u6709\u56fd\u7c4d", "to": "\u7f8e\u56fd"}, {"arrows": "to", "color": "#f7ce9e", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5973\u513f\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5973\u513f\u662f", "to": "\u8389\u6cfd\u5c14\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#f9ec94", "from": "\u7231\u5fb7\u534e\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5bf9\u7cbe\u795e\u5206\u6790\u6709\u5174\u8da3", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5bf9\u7cbe\u795e\u5206\u6790\u6709\u5174\u8da3", "to": "\u897f\u683c\u8499\u5fb7\u00b7\u4f5b\u6d1b\u4f0a\u5fb7"}, {"arrows": "to", "color": "#aeb3a1", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5199\u4fe1\u7ed9", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5199\u4fe1\u7ed9", "to": "\u5bcc\u5170\u514b\u6797\u00b7\u7f57\u65af\u798f"}, {"arrows": "to", "color": "#cadaf1", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u513f\u5b50\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u513f\u5b50\u662f", "to": "\u6c49\u65af\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#a4f4cc", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u9003\u907f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u9003\u907f", "to": "\u7eb3\u7cb9\u5fb7\u56fd"}, {"arrows": "to", "color": "#86efc3", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u521b\u7acb", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u521b\u7acb", "to": "\u76f8\u5bf9\u8bba"}, {"arrows": "to", "color": "#88df84", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u8363\u83b7", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u8363\u83b7", "to": "\u8bfa\u8d1d\u5c14\u7269\u7406\u5b66\u5956"}, {"arrows": "to", "color": "#81f3cc", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u604b\u7231", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u604b\u7231", "to": "\u739b\u683c\u4e3d\u5854\u00b7\u79d1\u5ae9\u79d1\u5a03"}, {"arrows": "to", "color": "#a5959f", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7ed3\u5a5a", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u7ed3\u5a5a", "to": "\u7c73\u5217\u5a03\u00b7\u9a6c\u5229\u5947"}, {"arrows": "to", "color": "#87a0e1", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5c45\u4f4f\u5728", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5c45\u4f4f\u5728", "to": "\u666e\u6797\u65af\u987f"}, {"arrows": "to", "color": "#f5ddcc", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u662f", "to": "\u666e\u9c81\u58eb\u79d1\u5b66\u9662"}, {"arrows": "to", "color": "#86efc3", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u521b\u7acb", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u521b\u7acb", "to": "\u91cf\u5b50\u529b\u5b66"}, {"arrows": "to", "color": "#9deac8", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u6bcd\u4eb2\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u6bcd\u4eb2\u662f", "to": "\u4fdd\u5229\u5a1c\u00b7\u79d1\u8d6b"}, {"arrows": "to", "color": "#cca69d", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5efa\u8bae", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5efa\u8bae", "to": "\u66fc\u54c8\u987f\u8ba1\u5212"}, {"arrows": "to", "color": "#a3f8ab", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u53d1\u73b0", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u53d1\u73b0", "to": "\u5149\u7535\u6548\u5e94"}, {"arrows": "to", "color": "#d1ad8b", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u53d1\u5c55", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u53d1\u5c55", "to": "\u72ed\u4e49\u76f8\u5bf9\u8bba"}, {"arrows": "to", "color": "#a3f8ab", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u53d1\u73b0", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u53d1\u73b0", "to": "\u8d28\u80fd\u7b49\u4ef7"}, {"arrows": "to", "color": "#bc9187", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u7236\u4eb2\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u7236\u4eb2\u662f", "to": "\u8d6b\u5c14\u66fc\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#ebeaf1", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u59b9\u59b9\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u59b9\u59b9\u662f", "to": "\u9a6c\u5a05\u00b7\u7231\u56e0\u65af\u5766"}, {"arrows": "to", "color": "#eed4dc", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u5973\u53cb\u662f", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u5973\u53cb\u662f", "to": "\u739b\u8389\u00b7\u6e29\u7279\u52d2"}, {"arrows": "to", "color": "#fec0d6", "from": "\u963f\u5c14\u4f2f\u7279\u00b7\u7231\u56e0\u65af\u5766", "label": "\u62e5\u6709\u56fd\u7c4d", "title": "\u003cb\u003eRelation:\u003c/b\u003e \u62e5\u6709\u56fd\u7c4d", "to": "\u5fb7\u56fd"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"nodes": {"font": {"size": 14, "face": "tahoma"}, "borderWidth": 1, "borderWidthSelected": 2}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 0.6}}, "color": {"inherit": false}, "smooth": {"type": "dynamic", "roundness": 0.2}, "font": {"size": 11, "face": "tahoma", "align": "middle", "strokeWidth": 2, "strokeColor": "#ffffff"}, "width": 1.5, "hoverWidth": 0.5, "selectionWidth": 1}, "physics": {"enabled": true, "barnesHut": {"gravitationalConstant": -25000, "centralGravity": 0.1, "springLength": 120, "springConstant": 0.05, "damping": 0.15, "avoidOverlap": 0.5}, "maxVelocity": 50, "minVelocity": 0.5, "solver": "barnesHut", "stabilization": {"enabled": true, "iterations": 1000, "updateInterval": 50, "onlyDynamicEdges": false, "fit": true}, "timestep": 0.5, "adaptiveTimestep": true}, "interaction": {"dragNodes": true, "dragView": true, "hideEdgesOnDrag": false, "hideNodesOnDrag": false, "hover": true, "hoverConnectedEdges": true, "keyboard": {"enabled": true}, "multiselect": true, "navigationButtons": true, "selectable": true, "selectConnectedEdges": true, "tooltipDelay": 200, "zoomView": true}, "manipulation": {"enabled": false}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>