<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ADD8E6", "id": "Ginny Weasley", "label": "Ginny Weasley", "shape": "dot", "size": 10.0, "title": "\u003cb\u003eEntity:\u003c/b\u003e Ginny Weasley\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 0"}, {"color": "#ADD8E6", "id": "James Potter", "label": "James Potter", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e James Potter\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "Hermione Granger", "label": "Hermione Granger", "shape": "dot", "size": 15.49306144334055, "title": "\u003cb\u003eEntity:\u003c/b\u003e Hermione Granger\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#ADD8E6", "id": "James Sirius", "label": "James Sirius", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e James Sirius\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "Lily Luna", "label": "Lily Luna", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e Lily Luna\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "Ron Weasley", "label": "Ron Weasley", "shape": "dot", "size": 16.931471805599454, "title": "\u003cb\u003eEntity:\u003c/b\u003e Ron Weasley\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 3"}, {"color": "#ADD8E6", "id": "Harry Potter", "label": "Harry Potter", "shape": "dot", "size": 20.9861228866811, "title": "\u003cb\u003eEntity:\u003c/b\u003e Harry Potter\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 8"}, {"color": "#ADD8E6", "id": "Ginny", "label": "Ginny", "shape": "dot", "size": 15.49306144334055, "title": "\u003cb\u003eEntity:\u003c/b\u003e Ginny\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 2"}, {"color": "#ADD8E6", "id": "Albus", "label": "Albus", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e Albus\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}, {"color": "#ADD8E6", "id": "Lily Potter", "label": "Lily Potter", "shape": "dot", "size": 13.465735902799727, "title": "\u003cb\u003eEntity:\u003c/b\u003e Lily Potter\u003cbr\u003e\u003cb\u003eDegree:\u003c/b\u003e 1"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#d9929a", "from": "Harry Potter", "label": "has mother", "title": "\u003cb\u003eRelation:\u003c/b\u003e has mother", "to": "Lily Potter"}, {"arrows": "to", "color": "#bffba2", "from": "Harry Potter", "label": "has wife", "title": "\u003cb\u003eRelation:\u003c/b\u003e has wife", "to": "Ginny"}, {"arrows": "to", "color": "#ee888a", "from": "Ginny", "label": "is sister of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is sister of", "to": "Ron Weasley"}, {"arrows": "to", "color": "#b8a6f9", "from": "Harry Potter", "label": "has child", "title": "\u003cb\u003eRelation:\u003c/b\u003e has child", "to": "James Sirius"}, {"arrows": "to", "color": "#b8a6f9", "from": "Harry Potter", "label": "has child", "title": "\u003cb\u003eRelation:\u003c/b\u003e has child", "to": "Lily Luna"}, {"arrows": "to", "color": "#f1a0b3", "from": "Harry Potter", "label": "has father", "title": "\u003cb\u003eRelation:\u003c/b\u003e has father", "to": "James Potter"}, {"arrows": "to", "color": "#9b9488", "from": "Harry Potter", "label": "is best friend of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is best friend of", "to": "Ron Weasley"}, {"arrows": "to", "color": "#9b9488", "from": "Harry Potter", "label": "is best friend of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is best friend of", "to": "Hermione Granger"}, {"arrows": "to", "color": "#b9ed9e", "from": "Ron Weasley", "label": "is husband of", "title": "\u003cb\u003eRelation:\u003c/b\u003e is husband of", "to": "Hermione Granger"}, {"arrows": "to", "color": "#b8a6f9", "from": "Harry Potter", "label": "has child", "title": "\u003cb\u003eRelation:\u003c/b\u003e has child", "to": "Albus"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"nodes": {"font": {"size": 14, "face": "tahoma"}, "borderWidth": 1, "borderWidthSelected": 2}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 0.6}}, "color": {"inherit": false}, "smooth": {"type": "dynamic", "roundness": 0.2}, "font": {"size": 11, "face": "tahoma", "align": "middle", "strokeWidth": 2, "strokeColor": "#ffffff"}, "width": 1.5, "hoverWidth": 0.5, "selectionWidth": 1}, "physics": {"enabled": true, "barnesHut": {"gravitationalConstant": -25000, "centralGravity": 0.1, "springLength": 120, "springConstant": 0.05, "damping": 0.15, "avoidOverlap": 0.5}, "maxVelocity": 50, "minVelocity": 0.5, "solver": "barnesHut", "stabilization": {"enabled": true, "iterations": 1000, "updateInterval": 50, "onlyDynamicEdges": false, "fit": true}, "timestep": 0.5, "adaptiveTimestep": true}, "interaction": {"dragNodes": true, "dragView": true, "hideEdgesOnDrag": false, "hideNodesOnDrag": false, "hover": true, "hoverConnectedEdges": true, "keyboard": {"enabled": true}, "multiselect": true, "navigationButtons": true, "selectable": true, "selectConnectedEdges": true, "tooltipDelay": 200, "zoomView": true}, "manipulation": {"enabled": false}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>